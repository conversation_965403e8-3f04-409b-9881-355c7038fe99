{"name": "bug-v3", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "pnpm run type-check && pnpm run build-only && pnpm run build:content && pnpm run build:background", "build:all": "pnpm run build-only && pnpm run build:scripts", "build:scripts": "pnpm run build:content && pnpm run build:background", "preview": "vite preview", "test": "pnpm test:unit && pnpm test:integration && pnpm test:e2e", "test:all": "pnpm test:unit && pnpm test:integration && pnpm test:verification && pnpm test:e2e", "test:unit": "vitest run test/unit", "test:integration": "vitest run test/integration", "test:verification": "vitest run test/verification", "test:e2e": "pnpm build && playwright test", "test:watch": "vitest test/unit", "test:userlogin": "node e2e/record-meta-login.e2e.test.cjs", "build-only": "vite build", "build:background": "vite build --config ./vite.background.config.ts", "build:content": "vite build --config ./vite.content.config.ts", "build:userscript": "BUILD_MINIFIED=false vite build --config ./vite.config.userscript.ts && node scripts/post-process-userscript.js", "build:userscript:min": "BUILD_MINIFIED=true vite build --config ./vite.config.userscript.ts && node scripts/post-process-userscript.js", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@eslint/js": "^9.34.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@google/genai": "^1.15.0", "@tailwindcss/postcss": "^4.1.12", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "ant-design-vue": "^4.2.6", "autoprefixer": "^10.4.21", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-promise": "^7.2.1", "globals": "^16.3.0", "monaco-editor": "^0.52.2", "openai": "^5.16.0", "pinia": "^3.0.3", "playwright": "^1.55.0", "tailwindcss": "^4.1.12", "uuid": "^11.1.0", "vue": "^3.5.20", "vue-eslint-parser": "^10.2.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@playwright/test": "^1.55.0", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.18.0", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.1.0", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.34.0", "eslint-plugin-playwright": "^2.2.2", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.5.1", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "sortablejs": "^1.15.6", "terser": "^5.43.1", "typescript": "~5.8.3", "unplugin-vue-components": "^29.0.0", "vite": "npm:rolldown-vite@^7.1.5", "vite-plugin-vue-devtools": "^8.0.1", "vitest": "^3.2.4", "vue-tsc": "^3.0.6"}}