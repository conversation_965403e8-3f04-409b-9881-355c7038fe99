{"manifest_version": 3, "name": "Mr <PERSON><PERSON><PERSON> for discourse", "version": "1.0", "description": "A simple example of a Chrome extension built with Manifest V3.", "action": {"default_popup": "popup.html", "default_title": "Click me!", "default_icon": {"48": "img/48.png", "64": "img/64.png", "128": "img/128.png", "512": "img/512.png", "1000": "img/1000.png", "1200": "img/1200.jpg"}}, "permissions": ["storage", "offscreen", "scripting"], "host_permissions": ["https://*.google.com/", "https://linux.do/*", "https://*.discourse.org/*", "<all_urls>"], "background": {"service_worker": "background.js"}, "icons": {"48": "img/48.png", "64": "img/64.png", "128": "img/128.png", "512": "img/512.png", "1000": "img/1000.png", "1200": "img/1200.jpg"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-script.js"]}], "options_ui": {"page": "options.html", "open_in_tab": true}, "web_accessible_resources": [{"resources": ["picker.css", "content-script.js", "static/config/converted_payload.json"], "matches": ["<all_urls>"]}]}